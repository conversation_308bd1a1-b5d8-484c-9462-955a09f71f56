<template>
  <section class="cert-template-container">
    <!-- 公司帐套管理区域 -->
    <div class="company-account-section">
      <div class="section-header">
        <h2 class="section-title">公司帐套管理</h2>
        <el-button type="primary" size="medium" icon="el-icon-edit" @click="showCompanyAccountDialog">
          管理帐套
        </el-button>
      </div>

      <!-- 层级切换按钮 -->
      <div class="hierarchy-switches">
        <!-- 第一级：公司选择 -->
        <div class="switch-level">
          <span class="switch-label">公司：</span>
          <el-select v-model="selectedCompanyId" placeholder="选择公司" @change="onCompanyChange" clearable>
            <el-option
              v-for="company in companyOptions"
              :key="company.companyId"
              :label="company.companyName"
              :value="company.companyId">
            </el-option>
          </el-select>
        </div>

        <!-- 第二级：核算类型选择 -->
        <div class="switch-level" v-if="selectedCompanyId">
          <span class="switch-label">核算类型：</span>
          <el-select v-model="selectedAccModel" placeholder="选择核算类型" @change="onAccModelChange" clearable>
            <el-option label="税务核算" :value="1"></el-option>
            <el-option label="股东核算" :value="2"></el-option>
          </el-select>
        </div>

        <!-- 第三级：帐套选择 -->
        <div class="switch-level" v-if="selectedCompanyId && selectedAccModel">
          <span class="switch-label">帐套：</span>
          <el-select v-model="selectedAppCode" placeholder="选择帐套" @change="onAppCodeChange" clearable>
            <el-option
              v-for="app in filteredAppOptions"
              :key="app.appCode"
              :label="app.appName"
              :value="app.appCode">
            </el-option>
          </el-select>
        </div>
      </div>

      <!-- 当前选择信息展示 -->
      <div class="current-selection" v-if="currentSelection.companyName">
        <div class="selection-info">
          <span class="info-item">
            <strong>公司：</strong>{{ currentSelection.companyName }}
          </span>
          <span class="info-item">
            <strong>核算类型：</strong>{{ currentSelection.accModelText }}
          </span>
          <span class="info-item" v-if="currentSelection.appName">
            <strong>帐套：</strong>{{ currentSelection.appName }}
          </span>
        </div>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchName"
        placeholder="请输入模板名称搜索"
        prefix-icon="el-icon-search"
        style="width: 300px; margin-bottom: 20px;"
        @input="handleSearch"
        clearable
      />
    </div>

    <!-- 添加模板按钮 -->
    <div class="add-template-btn-top">
      <el-button type="primary" size="medium" icon="el-icon-plus" @click="addTemplate">
        添加新模板
      </el-button>
    </div>

    <!-- 模板卡片列表 -->
    <div class="template-cards-container">
      <div v-if="certTemplList.length === 0" class="empty-state">
        <el-empty description="暂无模板数据" />
      </div>

      <div v-else class="cards-grid">
        <div
          v-for="template in certTemplList"
          :key="template.id"
          class="template-card"
        >
          <!-- 主模板卡片 -->
          <el-card class="parent-card" shadow="hover">
            <div class="card-header">
              <div class="template-info">
                <h3 class="template-name">{{ template.name }}</h3>
                <div class="template-meta">
                  <span class="meta-time">{{ formatTime(template.createTime) }}</span>
                  <span v-if="template.regularExpressions" class="meta-regex" :title="template.regularExpressions">
                    正则
                  </span>
                </div>
              </div>
              <div class="card-actions">
                <el-button type="primary" size="small" icon="el-icon-edit" @click="editTemplate(template)">
                  编辑
                </el-button>
              </div>
            </div>

            <!-- 子项展开/收起 -->
            <div v-if="template.hasChildren && template.children && template.children.length > 0" class="children-section">
              <div class="children-header">
                <span class="children-title">
                  会计科目 ({{ template.children.length }})
                </span>
              </div>

              <!-- 子项列表 - 默认显示前3条 -->
              <div class="children-list">
                <div
                  v-for="(child, index) in getVisibleChildren(template)"
                  :key="child.id"
                  class="child-item"
                >
                  <div class="child-content">
                    <div class="child-main">
                      <h4 class="child-name">{{ child.name }}</h4>
                      <p class="child-summary">{{ child.fieldSummary }}</p>
                    </div>
                    <div class="child-details">
                      <div class="detail-row">
                        <span class="detail-label">借贷:</span>
                        <span class="detail-value">{{ child.borrowing == 1 ? '借' : '贷' }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">摘要:</span>
                        <span class="detail-value">{{ child.templAbstract }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">排序:</span>
                        <span class="detail-value">{{ child.ordernum }}</span>
                      </div>
                    </div>
                    <div class="child-actions">
                      <el-button type="text" size="mini" @click="editChild(child, template)">
                        编辑
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 展开/收起按钮 -->
                <div v-if="template.children.length > 3" class="expand-toggle-btn">
                  <el-button
                    type="text"
                    size="small"
                    @click="toggleChildren(template)"
                    class="toggle-btn"
                  >
                    <i :class="template.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                    {{ template.expanded ? '收起' : `展开更多 (${template.children.length - 3})` }}
                  </el-button>
                </div>


              </div>
            </div>
              <!-- 添加子项按钮 -->
            <div class="add-child-btn">
              <el-button type="dashed" size="small" icon="el-icon-plus" @click="addChild(template)">
                添加会计科目
              </el-button>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 模板编辑对话框 -->
    <el-dialog
      :title="dialogMode === 'add' ? '添加模板' : '编辑模板'"
      :visible.sync="templateDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="templateForm"
        :model="templateForm"
        :rules="templateRules"
        label-width="120px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板编码" prop="code">
          <el-input v-model="templateForm.code" placeholder="请输入模板编码" />
        </el-form-item>
        <el-form-item label="应用编码" prop="appCode">
          <el-input v-model="templateForm.appCode" placeholder="请输入应用编码" />
        </el-form-item>
        <el-form-item label="正则表达式">
          <el-input
            v-model="templateForm.regularExpressions"
            type="textarea"
            :rows="3"
            placeholder="请输入正则表达式（可选）"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="templateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTemplate" :loading="saving">
          {{ dialogMode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 会计科目编辑对话框 -->
    <el-dialog
      :title="childDialogMode === 'add' ? '添加会计科目' : '编辑会计科目'"
      :visible.sync="childDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="childForm"
        :model="childForm"
        :rules="childRules"
        label-width="120px"
      >
        <el-form-item label="科目名称" prop="name">
          <el-input v-model="childForm.name" placeholder="请输入科目名称" />
        </el-form-item>
        <el-form-item label="字段编码" prop="fieldCode">
          <el-input v-model="childForm.fieldCode" placeholder="请输入字段编码" />
        </el-form-item>
        <el-form-item label="科目" prop="fieldSummary">
          <el-input v-model="childForm.fieldSummary" placeholder="请输入科目" />
        </el-form-item>
        <el-form-item label="会计科目" prop="accountingSubjects">
          <el-input v-model="childForm.accountingSubjects" placeholder="请输入会计科目代码" />
        </el-form-item>
        <el-form-item label="借贷方向" prop="borrowing">
          <el-select v-model="childForm.borrowing" placeholder="请选择借贷方向">
            <el-option label="借" :value="1" />
            <el-option label="贷" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板摘要" prop="templAbstract">
          <el-input v-model="childForm.templAbstract" placeholder="请输入模板摘要" />
        </el-form-item>
        <el-form-item label="排序号" prop="ordernum">
          <el-input-number v-model="childForm.ordernum" :min="1" placeholder="排序号" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="childDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveChild" :loading="saving">
          {{ childDialogMode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 公司帐套管理对话框 -->
    <el-dialog
      title="公司帐套管理"
      :visible.sync="companyAccountDialogVisible"
      width="1200px"
      :close-on-click-modal="false"
    >
      <div class="company-account-management">
        <!-- 公司帐套列表 -->
        <div class="account-list-section">
          <div class="list-header">
            <h3>公司帐套列表</h3>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addCompanyAccount">
              添加帐套
            </el-button>
          </div>

          <el-table :data="tokenCompanyList" border style="width: 100%" max-height="400">
            <el-table-column prop="companyName" label="公司名称" width="200" show-overflow-tooltip />
            <el-table-column prop="appName" label="帐套名称" width="150" />
            <el-table-column prop="appCode" label="帐套编码" width="120" />
            <el-table-column prop="accModel" label="核算类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.accModel === 1 ? 'success' : 'warning'">
                  {{ scope.row.accModel === 1 ? '税务核算' : '股东核算' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="bookid" label="账簿ID" width="150" show-overflow-tooltip />
            <el-table-column prop="orderNum" label="排序" width="80" />
            <el-table-column prop="createTime" label="创建时间" width="150">
              <template slot-scope="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="editCompanyAccount(scope.row)">
                  编辑
                </el-button>
                <el-button type="text" size="small" style="color: #f56c6c;" @click="deleteCompanyAccount(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="companyAccountDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 帐套编辑对话框 -->
    <el-dialog
      :title="accountDialogMode === 'add' ? '添加帐套' : '编辑帐套'"
      :visible.sync="accountDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="accountForm"
        :model="accountForm"
        :rules="accountRules"
        label-width="120px"
      >
        <el-form-item label="公司ID" prop="companyId">
          <el-input v-model="accountForm.companyId" placeholder="请输入公司ID" />
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="accountForm.companyName" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="帐套编码" prop="appCode">
          <el-input v-model="accountForm.appCode" placeholder="请输入帐套编码" />
        </el-form-item>
        <el-form-item label="帐套名称" prop="appName">
          <el-input v-model="accountForm.appName" placeholder="请输入帐套名称" />
        </el-form-item>
        <el-form-item label="账簿ID" prop="bookid">
          <el-input v-model="accountForm.bookid" placeholder="请输入账簿ID" />
        </el-form-item>
        <el-form-item label="核算类型" prop="accModel">
          <el-select v-model="accountForm.accModel" placeholder="请选择核算类型">
            <el-option label="税务核算" :value="1" />
            <el-option label="股东核算" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序号" prop="orderNum">
          <el-input-number v-model="accountForm.orderNum" :min="1" placeholder="排序号" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="accountDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAccount" :loading="saving">
          {{ accountDialogMode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-dialog>
  </section>
</template>
<script>
import { getCertTemplList,getCertTemplTempList,getTokenCompanyList,removeCertTemplTemp, saveCertTemplTemp,saveCertTemplVo,updateCertTemplVo,updateTokenCompany} from '@/api/system/baseInit'

export default {
  data() {
    return {
      certTemplList: [],
      searchName: '',
      originalList: [],

      // 公司帐套相关数据
      tokenCompanyList: [],
      selectedCompanyId: '',
      selectedAccModel: null,
      selectedAppCode: '',
      companyOptions: [],
      filteredAppOptions: [],
      currentSelection: {
        companyName: '',
        accModelText: '',
        appName: ''
      },

      // 公司帐套管理对话框
      companyAccountDialogVisible: false,
      accountDialogVisible: false,
      accountDialogMode: 'add', // 'add' | 'edit'
      accountForm: {
        id: null,
        companyId: '',
        companyName: '',
        appCode: '',
        appName: '',
        bookid: '',
        accModel: 1,
        orderNum: 1
      },
      accountRules: {
        companyId: [
          { required: true, message: '请输入公司ID', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入公司名称', trigger: 'blur' }
        ],
        appCode: [
          { required: true, message: '请输入帐套编码', trigger: 'blur' }
        ],
        appName: [
          { required: true, message: '请输入帐套名称', trigger: 'blur' }
        ],
        bookid: [
          { required: true, message: '请输入账簿ID', trigger: 'blur' }
        ],
        accModel: [
          { required: true, message: '请选择核算类型', trigger: 'change' }
        ],
        orderNum: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      },

      // 模板对话框相关
      templateDialogVisible: false,
      dialogMode: 'add', // 'add' | 'edit'
      templateForm: {
        id: null,
        name: '',
        code: '',
        appCode: '',
        regularExpressions: ''
      },
      templateRules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入模板编码', trigger: 'blur' },
          { validator: this.validateCode, trigger: 'blur' }
        ],
        appCode: [
          { required: true, message: '请输入应用编码', trigger: 'blur' }
        ]
      },

      // 会计科目对话框相关
      childDialogVisible: false,
      childDialogMode: 'add', // 'add' | 'edit'
      currentTemplate: null, // 当前操作的模板
      childForm: {
        id: null,
        name: '',
        fieldCode: '',
        fieldSummary: '',
        accountingSubjects: '',
        borrowing: 1,
        templAbstract: '',
        ordernum: 1,
        certTemplId: null
      },
      childRules: {
        name: [
          { required: true, message: '请输入科目名称', trigger: 'blur' }
        ],
        fieldCode: [
          { required: true, message: '请输入字段编码', trigger: 'blur' }
        ],
        fieldSummary: [
          { required: true, message: '请输入科目', trigger: 'blur' }
        ],
        accountingSubjects: [
          { required: true, message: '请输入会计科目代码', trigger: 'blur' }
        ],
        borrowing: [
          { required: true, message: '请选择借贷方向', trigger: 'change' }
        ],
        templAbstract: [
          { required: true, message: '请输入模板摘要', trigger: 'blur' }
        ],
        ordernum: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      },

      saving: false
    }
  },
  computed: {

  },
  created() {
    this.loadCertTemplList()
    this.loadTokenCompanyList()
  },
  methods: {
    loadTokenCompanyList(){
      getTokenCompanyList().then(res=>{
        if (res && res.list) {
          this.tokenCompanyList = res.list
          this.processCompanyData()
        }
      })
    },

    // 处理公司数据，生成选项列表
    processCompanyData() {
      // 生成公司选项（去重）
      const companyMap = new Map()
      this.tokenCompanyList.forEach(item => {
        if (!companyMap.has(item.companyId)) {
          companyMap.set(item.companyId, {
            companyId: item.companyId,
            companyName: item.companyName
          })
        }
      })
      this.companyOptions = Array.from(companyMap.values())
    },

    // 公司选择变化
    onCompanyChange() {
      this.selectedAccModel = null
      this.selectedAppCode = ''
      this.filteredAppOptions = []
      this.updateCurrentSelection()
    },

    // 核算类型选择变化
    onAccModelChange() {
      this.selectedAppCode = ''
      this.updateFilteredAppOptions()
      this.updateCurrentSelection()
    },

    // 帐套选择变化
    onAppCodeChange() {
      this.updateCurrentSelection()
    },

    // 更新过滤后的帐套选项
    updateFilteredAppOptions() {
      if (this.selectedCompanyId && this.selectedAccModel !== null) {
        const filteredApps = this.tokenCompanyList.filter(item =>
          item.companyId === this.selectedCompanyId &&
          item.accModel === this.selectedAccModel
        )

        // 去重帐套
        const appMap = new Map()
        filteredApps.forEach(item => {
          if (!appMap.has(item.appCode)) {
            appMap.set(item.appCode, {
              appCode: item.appCode,
              appName: item.appName
            })
          }
        })
        this.filteredAppOptions = Array.from(appMap.values())
      } else {
        this.filteredAppOptions = []
      }
    },

    // 更新当前选择信息
    updateCurrentSelection() {
      const company = this.companyOptions.find(c => c.companyId === this.selectedCompanyId)
      const app = this.filteredAppOptions.find(a => a.appCode === this.selectedAppCode)

      this.currentSelection = {
        companyName: company ? company.companyName : '',
        accModelText: this.selectedAccModel === 1 ? '税务核算' : this.selectedAccModel === 2 ? '股东核算' : '',
        appName: app ? app.appName : ''
      }
    },

    // 显示公司帐套管理对话框
    showCompanyAccountDialog() {
      this.companyAccountDialogVisible = true
    },

    // 添加帐套
    addCompanyAccount() {
      this.accountDialogMode = 'add'
      this.resetAccountForm()
      this.accountDialogVisible = true
    },

    // 编辑帐套
    editCompanyAccount(account) {
      this.accountDialogMode = 'edit'
      this.accountForm = {
        id: account.id,
        companyId: account.companyId,
        companyName: account.companyName,
        appCode: account.appCode,
        appName: account.appName,
        bookid: account.bookid,
        accModel: account.accModel,
        orderNum: account.orderNum
      }
      this.accountDialogVisible = true
    },

    // 删除帐套
    deleteCompanyAccount(account) {
      this.$confirm('确定要删除这个帐套吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用删除API
        this.$message.success('删除成功')
        this.loadTokenCompanyList()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 重置帐套表单
    resetAccountForm() {
      this.accountForm = {
        id: null,
        companyId: '',
        companyName: '',
        appCode: '',
        appName: '',
        bookid: '',
        accModel: 1,
        orderNum: 1
      }
      if (this.$refs.accountForm) {
        this.$refs.accountForm.clearValidate()
      }
    },

    // 保存帐套
    saveAccount() {
      this.$refs.accountForm.validate((valid) => {
        if (valid) {
          this.saving = true

          // 这里应该调用保存或更新API
          const apiCall = this.accountDialogMode === 'add'
            ? updateTokenCompany(this.accountForm) // 使用现有的API
            : updateTokenCompany(this.accountForm)

          apiCall.then(() => {
            this.$message.success(this.accountDialogMode === 'add' ? '添加成功' : '保存成功')
            this.accountDialogVisible = false
            this.loadTokenCompanyList()
          }).catch(err => {
            this.$message.error(err.message || '操作失败')
          }).finally(() => {
            this.saving = false
          })
        }
      })
    },
    loadCertTemplList() {
      getCertTemplList(this.searchName).then(res => {
        // 确保每个模板都有 expanded 属性
        const list = (res.list || res || []).map(template => ({
          ...template,
          expanded: false
        }))
        this.certTemplList = list
        this.originalList = [...list]
      })
    },

    handleSearch() {
      this.loadCertTemplList()
    },

    getVisibleChildren(template) {
      if (!template.children) return []
      if (template.expanded) {
        return template.children
      }
      return template.children.slice(0, 3)
    },

    toggleChildren(template) {
      this.$set(template, 'expanded', !template.expanded)
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      // 将完整时间格式化为简短格式
      const date = new Date(timeStr)
      const now = new Date()
      const diffTime = now - date
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === 1) {
        return '昨天'
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else if (diffDays < 30) {
        const weeks = Math.floor(diffDays / 7)
        return `${weeks}周前`
      } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30)
        return `${months}月前`
      } else {
        return timeStr.split(' ')[0] // 返回日期部分
      }
    },

    // 模板相关方法
    addTemplate() {
      this.dialogMode = 'add'
      this.resetTemplateForm()
      this.templateDialogVisible = true
    },

    editTemplate(template) {
      this.dialogMode = 'edit'
      this.templateForm = {
        id: template.id,
        name: template.name,
        code: template.code,
        appCode: template.appCode,
        regularExpressions: template.regularExpressions || ''
      }
      this.templateDialogVisible = true
    },

    resetTemplateForm() {
      this.templateForm = {
        id: null,
        name: '',
        code: '',
        appCode: 'prod1', // 设置默认值为 prod1
        regularExpressions: ''
      }
      if (this.$refs.templateForm) {
        this.$refs.templateForm.clearValidate()
      }
    },

    // 验证编码是否重复
    validateCode(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      // 如果是编辑模式且编码没有改变，则不需要验证
      if (this.dialogMode === 'edit' && this.templateForm.id) {
        const currentTemplate = this.certTemplList.find(t => t.id === this.templateForm.id)
        if (currentTemplate && currentTemplate.code === value) {
          callback()
          return
        }
      }

      // 检查编码是否已存在
      const existingTemplate = this.certTemplList.find(template =>
        template.code === value && template.id !== this.templateForm.id
      )

      if (existingTemplate) {
        callback(new Error('该编码已存在，请使用其他编码'))
      } else {
        callback()
      }
    },

    saveTemplate() {
      this.$refs.templateForm.validate((valid) => {
        if (valid) {
          this.saving = true
          // 去除parent包装，直接使用扁平化数据结构
          const templateData = {
            ...this.templateForm,
            children: []
          }

          const apiCall = this.dialogMode === 'add'
            ? saveCertTemplVo(templateData)
            : updateCertTemplVo(templateData)

          apiCall.then(res => {
            this.$message.success(this.dialogMode === 'add' ? '添加成功' : '保存成功')
            this.templateDialogVisible = false
            this.loadCertTemplList()
          }).catch(err => {
            this.$message.error(err.message || '操作失败')
          }).finally(() => {
            this.saving = false
          })
        }
      })
    },

    // 会计科目相关方法
    addChild(template) {
      this.childDialogMode = 'add'
      this.currentTemplate = template
      this.resetChildForm()
      this.childForm.certTemplId = template.id
      // 设置默认排序号为当前子项数量+1
      this.childForm.ordernum = (template.children ? template.children.length : 0) + 1
      this.childDialogVisible = true
    },

    editChild(child, template) {
      this.childDialogMode = 'edit'
      this.currentTemplate = template
      this.childForm = {
        id: child.id,
        name: child.name,
        fieldCode: child.fieldCode,
        fieldSummary: child.fieldSummary,
        accountingSubjects: child.accountingSubjects,
        borrowing: child.borrowing,
        templAbstract: child.templAbstract,
        ordernum: child.ordernum,
        certTemplId: template.id
      }
      this.childDialogVisible = true
    },

    resetChildForm() {
      this.childForm = {
        id: null,
        name: '',
        fieldCode: '',
        fieldSummary: '',
        accountingSubjects: '',
        borrowing: 1,
        templAbstract: '',
        ordernum: 1,
        certTemplId: null
      }
      if (this.$refs.childForm) {
        this.$refs.childForm.clearValidate()
      }
    },

    saveChild() {
      this.$refs.childForm.validate((valid) => {
        if (valid) {
          this.saving = true

          // 构建完整的模板数据
          const children = [...(this.currentTemplate.children || [])]

          if (this.childDialogMode === 'add') {
            // 添加新的子项
            children.push({ ...this.childForm })
          } else {
            // 更新现有子项
            const index = children.findIndex(child => child.id === this.childForm.id)
            if (index !== -1) {
              children[index] = { ...this.childForm }
            }
          }

          // 去除parent包装，直接使用扁平化数据结构
          const templateData = {
            id: this.currentTemplate.id,
            name: this.currentTemplate.name,
            code: this.currentTemplate.code,
            appCode: this.currentTemplate.appCode,
            regularExpressions: this.currentTemplate.regularExpressions || '',
            children: children
          }

          updateCertTemplVo(templateData).then(res => {
            this.$message.success(this.childDialogMode === 'add' ? '添加成功' : '保存成功')
            this.childDialogVisible = false
            this.loadCertTemplList()
          }).catch(err => {
            this.$message.error(err.message || '操作失败')
          }).finally(() => {
            this.saving = false
          })
        }
      })
    }
  }
}
</script>
<style scoped>
.cert-template-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 公司帐套管理区域样式 */
.company-account-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.hierarchy-switches {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.switch-level {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.current-selection {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.selection-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.info-item {
  font-size: 14px;
  color: #606266;
}

.info-item strong {
  color: #303133;
  margin-right: 4px;
}

/* 公司帐套管理对话框样式 */
.company-account-management {
  padding: 0;
}

.account-list-section {
  margin-bottom: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.template-cards-container {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.template-card {
  width: 100%;
}

.parent-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.parent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.template-info {
  flex: 1;
}

.template-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 16px;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.meta-time {
  font-size: 12px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 2px 8px;
  border-radius: 12px;
}

.meta-regex {
  font-size: 12px;
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #b3e19d;
  cursor: help;
}

.children-section {
  margin-top: 16px;
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.children-header {
  padding: 8px 0;
}

.children-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.children-title i {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.children-list {
  margin-top: 12px;
}

.child-item {
  background-color: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.child-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.child-content {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 16px;
  align-items: start;
}

.child-main {
  min-width: 0;
}

.child-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.child-summary {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  word-break: break-all;
}

.child-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-row {
  display: flex;
  font-size: 14px;
  line-height: 1.4;
}

.detail-label {
  color: #909399;
  width: 50px;
  flex-shrink: 0;
}

.detail-value {
  color: #606266;
  flex: 1;
  word-break: break-all;
}

.child-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-shrink: 0;
}

.expand-toggle-btn {
  text-align: center;
  padding: 12px 0;
  border-top: 1px dashed #e4e7ed;
  margin-top: 8px;
}

.toggle-btn {
  color: #409eff;
  font-size: 14px;
}

.toggle-btn:hover {
  color: #66b1ff;
}

.toggle-btn i {
  margin-right: 4px;
}

.add-child-btn {
  text-align: center;
  padding: 16px;
  border-top: 1px dashed #d3d4d6;
  margin-top: 8px;
}

.add-template-btn-top {
  text-align: left;
  margin-bottom: 20px;
}

.add-template-btn {
  text-align: center;
  padding: 20px 0;
}

/* 动画效果 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }

  .child-content {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .child-actions {
    flex-direction: row;
    justify-content: flex-end;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .card-actions {
    margin-left: 0;
    margin-top: 12px;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .cert-template-container {
    padding: 12px;
  }

  .search-bar {
    margin-bottom: 16px;
  }

  .search-bar .el-input {
    width: 100% !important;
  }
}
</style>
